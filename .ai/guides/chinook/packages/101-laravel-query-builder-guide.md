# Spatie Laravel Query Builder Guide

## Table of Contents

- [Overview](#overview)
  - [Key Features](#key-features)
  - [Architecture Overview](#architecture-overview)
- [Installation & Configuration](#installation--configuration)
  - [Package Installation](#package-installation)
  - [Configuration Setup](#configuration-setup)
- [API Endpoint Design](#api-endpoint-design)
  - [Basic Query Builder Implementation](#basic-query-builder-implementation)
  - [Track Data DTO](#track-data-dto)
- [Advanced Filtering Strategies](#advanced-filtering-strategies)
  - [Custom Filter Classes](#custom-filter-classes)
  - [Model Scopes for Complex Filtering](#model-scopes-for-complex-filtering)
  - [Advanced Controller with Custom Filters](#advanced-controller-with-custom-filters)
- [Sorting & Pagination](#sorting--pagination)
  - [Advanced Sorting Implementation](#advanced-sorting-implementation)
  - [Pagination Strategies](#pagination-strategies)
- [Laravel Data Integration](#laravel-data-integration)
  - [Advanced Data Transfer Objects](#advanced-data-transfer-objects)
  - [Request Data Validation](#request-data-validation)
- [Security Considerations](#security-considerations)
  - [Authorization Middleware](#authorization-middleware)
  - [Input Sanitization](#input-sanitization)
- [Performance Optimization](#performance-optimization)
  - [Query Optimization Service](#query-optimization-service)
- [Testing Strategies](#testing-strategies)
  - [Query Builder Tests](#query-builder-tests)
- [Best Practices](#best-practices)
  - [API Design Guidelines](#api-design-guidelines)
  - [Example API Documentation](#example-api-documentation)

## Overview

Spatie Laravel Query Builder provides a powerful and flexible way to build API endpoints with filtering, sorting, and including relationships. This guide demonstrates how to create type-safe, performant APIs using Query Builder with Laravel Data DTOs for enterprise applications.

### Key Features

- **Flexible Filtering**: Support for exact matches, partial matches, and custom filters
- **Dynamic Sorting**: Multi-column sorting with custom sort logic
- **Relationship Inclusion**: Eager loading with nested relationships
- **Type Safety**: Integration with Laravel Data for validated DTOs
- **Performance**: Optimized queries with proper indexing strategies
- **Security**: Built-in protection against SQL injection and unauthorized access

### Architecture Overview

```mermaid
graph TB
    subgraph "API Request Flow"
        A[HTTP Request]
        B[Query Builder]
        C[Filters & Sorts]
        D[Database Query]
        E[Laravel Data DTO]
        F[JSON Response]
    end
    
    subgraph "Security Layer"
        G[Authorization]
        H[Input Validation]
        I[Rate Limiting]
    end
    
    subgraph "Performance Layer"
        J[Query Optimization]
        K[Caching]
        L[Pagination]
    end
    
    A --> G
    G --> H
    H --> B
    B --> C
    C --> J
    J --> D
    D --> E
    E --> F
    
    B --> I
    C --> K
    D --> L
    
    style A fill:#1976d2,color:#fff
    style B fill:#388e3c,color:#fff
    style E fill:#f57c00,color:#fff
    style G fill:#d32f2f,color:#fff
```

## Installation & Configuration

### Package Installation

```bash
# Install Query Builder
composer require spatie/laravel-query-builder

# Install Laravel Data for DTOs
composer require spatie/laravel-data

# Publish configuration (optional)
php artisan vendor:publish --provider="Spatie\QueryBuilder\QueryBuilderServiceProvider"
```

### Configuration Setup

```php
<?php

// config/query-builder.php
return [
    /*
     * By default, the package will use the `include`, `filter`, `sort`
     * and `fields` query parameters as described in the readme.
     */
    'parameters' => [
        'include' => 'include',
        'filter' => 'filter',
        'sort' => 'sort',
        'fields' => 'fields',
        'append' => 'append',
    ],

    /*
     * Related model counts are included using the relationship name suffixed with this string.
     */
    'count_suffix' => 'Count',

    /*
     * By default, the package will throw an `InvalidFilterQuery` exception when a filter in the URL
     * is not allowed in the `allowedFilters()` method.
     */
    'disable_invalid_filter_query_exception' => false,

    /*
     * By default, the package will throw an `InvalidSortQuery` exception when a sort in the URL
     * is not allowed in the `allowedSorts()` method.
     */
    'disable_invalid_sort_query_exception' => false,

    /*
     * By default, the package will throw an `InvalidIncludeQuery` exception when an include in the URL
     * is not allowed in the `allowedIncludes()` method.
     */
    'disable_invalid_include_query_exception' => false,
];
```

## API Endpoint Design

### Basic Query Builder Implementation

```php
<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Track;
use App\Data\TrackData;
use Illuminate\Http\Request;
use Spatie\QueryBuilder\QueryBuilder;
use Spatie\QueryBuilder\AllowedFilter;
use Spatie\QueryBuilder\AllowedSort;
use Spatie\QueryBuilder\AllowedInclude;

class TrackController extends Controller
{
    /**
     * Display a listing of tracks with filtering, sorting, and includes
     */
    public function index(Request $request): \Illuminate\Http\JsonResponse
    {
        $tracks = QueryBuilder::for(Track::class)
            ->allowedFilters([
                'name',
                'composer',
                AllowedFilter::exact('album_id'),
                AllowedFilter::exact('media_type_id'),
                AllowedFilter::exact('genre_id'),
                AllowedFilter::scope('duration_between'),
                AllowedFilter::callback('price_range', function ($query, $value) {
                    $range = explode(',', $value);
                    if (count($range) === 2) {
                        $query->whereBetween('unit_price', [(float)$range[0], (float)$range[1]]);
                    }
                }),
                AllowedFilter::callback('search', function ($query, $value) {
                    $query->where(function ($q) use ($value) {
                        $q->where('name', 'like', "%{$value}%")
                          ->orWhere('composer', 'like', "%{$value}%")
                          ->orWhereHas('album', function ($albumQuery) use ($value) {
                              $albumQuery->where('title', 'like', "%{$value}%");
                          });
                    });
                }),
            ])
            ->allowedSorts([
                'name',
                'composer',
                'milliseconds',
                'unit_price',
                'created_at',
                AllowedSort::field('album_title', 'album.title'),
                AllowedSort::field('artist_name', 'album.artist.name'),
            ])
            ->allowedIncludes([
                'album',
                'album.artist',
                'genre',
                'mediaType',
                'playlistTracks',
                'invoiceLines',
            ])
            ->allowedFields([
                'tracks.id',
                'tracks.name',
                'tracks.composer',
                'tracks.milliseconds',
                'tracks.bytes',
                'tracks.unit_price',
                'album.id',
                'album.title',
                'album.artist.name',
            ])
            ->paginate($request->get('per_page', 15))
            ->withQueryString();

        return response()->json([
            'data' => TrackData::collection($tracks->items()),
            'meta' => [
                'current_page' => $tracks->currentPage(),
                'last_page' => $tracks->lastPage(),
                'per_page' => $tracks->perPage(),
                'total' => $tracks->total(),
            ],
            'links' => [
                'first' => $tracks->url(1),
                'last' => $tracks->url($tracks->lastPage()),
                'prev' => $tracks->previousPageUrl(),
                'next' => $tracks->nextPageUrl(),
            ],
        ]);
    }

    /**
     * Display the specified track
     */
    public function show(Track $track): \Illuminate\Http\JsonResponse
    {
        $track = QueryBuilder::for(Track::where('id', $track->id))
            ->allowedIncludes([
                'album',
                'album.artist',
                'genre',
                'mediaType',
                'playlistTracks.playlist',
                'invoiceLines.invoice',
            ])
            ->first();

        return response()->json([
            'data' => TrackData::from($track),
        ]);
    }
}
```

### Track Data DTO

```php
<?php

namespace App\Data;

use App\Models\Track;
use Spatie\LaravelData\Data;
use Spatie\LaravelData\Lazy;
use Spatie\LaravelData\Attributes\Validation\Required;
use Spatie\LaravelData\Attributes\Validation\Min;
use Spatie\LaravelData\Attributes\Validation\Max;

class TrackData extends Data
{
    public function __construct(
        public int $id,
        #[Required, Max(200)]
        public string $name,
        public ?string $composer,
        #[Min(0)]
        public int $milliseconds,
        #[Min(0)]
        public ?int $bytes,
        #[Min(0)]
        public float $unit_price,
        public int $album_id,
        public int $media_type_id,
        public ?int $genre_id,
        public Lazy|AlbumData|null $album,
        public Lazy|GenreData|null $genre,
        public Lazy|MediaTypeData|null $mediaType,
        /** @var PlaylistTrackData[] */
        public Lazy|array $playlistTracks,
        /** @var InvoiceLineData[] */
        public Lazy|array $invoiceLines,
    ) {}

    public static function fromModel(Track $track): self
    {
        return new self(
            id: $track->id,
            name: $track->name,
            composer: $track->composer,
            milliseconds: $track->milliseconds,
            bytes: $track->bytes,
            unit_price: $track->unit_price,
            album_id: $track->album_id,
            media_type_id: $track->media_type_id,
            genre_id: $track->genre_id,
            album: Lazy::whenLoaded('album', $track, fn() => AlbumData::from($track->album)),
            genre: Lazy::whenLoaded('genre', $track, fn() => GenreData::from($track->genre)),
            mediaType: Lazy::whenLoaded('mediaType', $track, fn() => MediaTypeData::from($track->mediaType)),
            playlistTracks: Lazy::whenLoaded('playlistTracks', $track, fn() => 
                PlaylistTrackData::collection($track->playlistTracks)
            ),
            invoiceLines: Lazy::whenLoaded('invoiceLines', $track, fn() => 
                InvoiceLineData::collection($track->invoiceLines)
            ),
        );
    }

    /**
     * Get formatted duration
     */
    public function getDurationAttribute(): string
    {
        $seconds = $this->milliseconds / 1000;
        $minutes = floor($seconds / 60);
        $seconds = $seconds % 60;
        
        return sprintf('%d:%02d', $minutes, $seconds);
    }

    /**
     * Get formatted file size
     */
    public function getFileSizeAttribute(): ?string
    {
        if (!$this->bytes) {
            return null;
        }
        
        $units = ['B', 'KB', 'MB', 'GB'];
        $bytes = $this->bytes;
        
        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }
        
        return round($bytes, 2) . ' ' . $units[$i];
    }
}
```

## Advanced Filtering Strategies

### Custom Filter Classes

```php
<?php

namespace App\QueryFilters;

use Illuminate\Database\Eloquent\Builder;
use Spatie\QueryBuilder\Filters\Filter;

class DurationRangeFilter implements Filter
{
    public function __invoke(Builder $query, $value, string $property): Builder
    {
        $ranges = [
            'short' => [0, 180000], // 0-3 minutes in milliseconds
            'medium' => [180000, 300000], // 3-5 minutes
            'long' => [300000, PHP_INT_MAX], // 5+ minutes
        ];

        if (isset($ranges[$value])) {
            [$min, $max] = $ranges[$value];
            return $query->whereBetween('milliseconds', [$min, $max]);
        }

        // Handle custom range format: "min,max"
        if (str_contains($value, ',')) {
            [$min, $max] = explode(',', $value);
            return $query->whereBetween('milliseconds', [(int)$min, (int)$max]);
        }

        return $query;
    }
}

class PopularityFilter implements Filter
{
    public function __invoke(Builder $query, $value, string $property): Builder
    {
        return match ($value) {
            'trending' => $query->withCount('invoiceLines')
                ->having('invoice_lines_count', '>', 10)
                ->orderByDesc('invoice_lines_count'),
            'popular' => $query->withCount('playlistTracks')
                ->having('playlist_tracks_count', '>', 5),
            'new' => $query->where('created_at', '>=', now()->subDays(30)),
            default => $query
        };
    }
}

class GenreHierarchyFilter implements Filter
{
    public function __invoke(Builder $query, $value, string $property): Builder
    {
        // Support filtering by genre hierarchy (parent and child genres)
        return $query->whereHas('genre', function ($genreQuery) use ($value) {
            $genreQuery->where('name', $value)
                ->orWhereHas('parent', function ($parentQuery) use ($value) {
                    $parentQuery->where('name', $value);
                });
        });
    }
}
```

### Model Scopes for Complex Filtering

```php
<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Builder;

class Track extends Model
{
    /**
     * Scope for filtering by artist
     */
    public function scopeByArtist(Builder $query, string $artistName): Builder
    {
        return $query->whereHas('album.artist', function ($artistQuery) use ($artistName) {
            $artistQuery->where('name', 'like', "%{$artistName}%");
        });
    }

    /**
     * Scope for filtering by release date range
     */
    public function scopeReleasedBetween(Builder $query, string $dateRange): Builder
    {
        $dates = explode(',', $dateRange);

        if (count($dates) === 2) {
            return $query->whereHas('album', function ($albumQuery) use ($dates) {
                $albumQuery->whereBetween('release_date', [
                    \Carbon\Carbon::parse($dates[0]),
                    \Carbon\Carbon::parse($dates[1])
                ]);
            });
        }

        return $query;
    }

    /**
     * Scope for filtering by price range
     */
    public function scopePriceRange(Builder $query, string $priceRange): Builder
    {
        $prices = explode(',', $priceRange);

        if (count($prices) === 2) {
            return $query->whereBetween('unit_price', [
                (float)$prices[0],
                (float)$prices[1]
            ]);
        }

        return $query;
    }

    /**
     * Scope for duration filtering
     */
    public function scopeDurationBetween(Builder $query, string $durationRange): Builder
    {
        $durations = explode(',', $durationRange);

        if (count($durations) === 2) {
            // Convert minutes to milliseconds
            $minMs = (int)$durations[0] * 60 * 1000;
            $maxMs = (int)$durations[1] * 60 * 1000;

            return $query->whereBetween('milliseconds', [$minMs, $maxMs]);
        }

        return $query;
    }
}
```

### Advanced Controller with Custom Filters

```php
<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Track;
use App\Data\TrackData;
use App\QueryFilters\DurationRangeFilter;
use App\QueryFilters\PopularityFilter;
use App\QueryFilters\GenreHierarchyFilter;
use Illuminate\Http\Request;
use Spatie\QueryBuilder\QueryBuilder;
use Spatie\QueryBuilder\AllowedFilter;
use Spatie\QueryBuilder\AllowedSort;

class AdvancedTrackController extends Controller
{
    public function index(Request $request): \Illuminate\Http\JsonResponse
    {
        $tracks = QueryBuilder::for(Track::class)
            ->allowedFilters([
                // Basic filters
                'name',
                'composer',
                AllowedFilter::exact('album_id'),
                AllowedFilter::exact('genre_id'),

                // Custom filter classes
                AllowedFilter::custom('duration', new DurationRangeFilter()),
                AllowedFilter::custom('popularity', new PopularityFilter()),
                AllowedFilter::custom('genre_hierarchy', new GenreHierarchyFilter()),

                // Scope filters
                AllowedFilter::scope('by_artist'),
                AllowedFilter::scope('released_between'),
                AllowedFilter::scope('price_range'),

                // Complex search filter
                AllowedFilter::callback('q', function ($query, $value) {
                    $query->where(function ($q) use ($value) {
                        $q->where('name', 'like', "%{$value}%")
                          ->orWhere('composer', 'like', "%{$value}%")
                          ->orWhereHas('album', function ($albumQuery) use ($value) {
                              $albumQuery->where('title', 'like', "%{$value}%")
                                  ->orWhereHas('artist', function ($artistQuery) use ($value) {
                                      $artistQuery->where('name', 'like', "%{$value}%");
                                  });
                          });
                    });
                }),
            ])
            ->allowedSorts([
                'name',
                'composer',
                'milliseconds',
                'unit_price',
                'created_at',
                AllowedSort::field('album_title', 'album.title'),
                AllowedSort::field('artist_name', 'album.artist.name'),
            ])
            ->allowedIncludes([
                'album',
                'album.artist',
                'genre',
                'mediaType',
                'playlistTracks.playlist',
            ])
            ->paginate($request->get('per_page', 15))
            ->withQueryString();

        return response()->json([
            'data' => TrackData::collection($tracks->items()),
            'meta' => $this->buildMetadata($tracks, $request),
            'links' => $this->buildLinks($tracks),
        ]);
    }

    private function buildMetadata($tracks, Request $request): array
    {
        return [
            'current_page' => $tracks->currentPage(),
            'last_page' => $tracks->lastPage(),
            'per_page' => $tracks->perPage(),
            'total' => $tracks->total(),
            'filters_applied' => $request->get('filter', []),
            'sort_applied' => $request->get('sort'),
            'includes_applied' => $request->get('include'),
        ];
    }

    private function buildLinks($tracks): array
    {
        return [
            'first' => $tracks->url(1),
            'last' => $tracks->url($tracks->lastPage()),
            'prev' => $tracks->previousPageUrl(),
            'next' => $tracks->nextPageUrl(),
            'self' => $tracks->url($tracks->currentPage()),
        ];
    }
}
```

## Sorting & Pagination

### Advanced Sorting Implementation

```php
<?php

namespace App\QuerySorts;

use Illuminate\Database\Eloquent\Builder;
use Spatie\QueryBuilder\Sorts\Sort;

class PopularitySort implements Sort
{
    public function __invoke(Builder $query, bool $descending, string $property): Builder
    {
        return $query
            ->withCount('invoiceLines')
            ->orderBy('invoice_lines_count', $descending ? 'desc' : 'asc');
    }
}

class RelevanceSort implements Sort
{
    private string $searchTerm;

    public function __construct(string $searchTerm = '')
    {
        $this->searchTerm = $searchTerm;
    }

    public function __invoke(Builder $query, bool $descending, string $property): Builder
    {
        if (empty($this->searchTerm)) {
            return $query->orderBy('created_at', 'desc');
        }

        return $query
            ->selectRaw('*, (
                CASE
                    WHEN name LIKE ? THEN 3
                    WHEN composer LIKE ? THEN 2
                    ELSE 1
                END
            ) as relevance_score', [
                "%{$this->searchTerm}%",
                "%{$this->searchTerm}%"
            ])
            ->orderBy('relevance_score', $descending ? 'desc' : 'asc')
            ->orderBy('name', 'asc');
    }
}
```

### Pagination Strategies

```php
<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Track;
use App\Data\TrackData;
use Illuminate\Http\Request;
use Spatie\QueryBuilder\QueryBuilder;

class PaginatedTrackController extends Controller
{
    public function index(Request $request): \Illuminate\Http\JsonResponse
    {
        $paginationType = $request->get('pagination_type', 'standard');

        $query = QueryBuilder::for(Track::class)
            ->allowedFilters(['name', 'composer', 'genre_id'])
            ->allowedSorts(['name', 'created_at', 'unit_price'])
            ->allowedIncludes(['album', 'genre']);

        $result = match ($paginationType) {
            'cursor' => $this->cursorPaginate($query, $request),
            'simple' => $this->simplePaginate($query, $request),
            default => $this->standardPaginate($query, $request)
        };

        return response()->json($result);
    }

    private function standardPaginate($query, Request $request): array
    {
        $tracks = $query->paginate($request->get('per_page', 15))->withQueryString();

        return [
            'data' => TrackData::collection($tracks->items()),
            'meta' => [
                'current_page' => $tracks->currentPage(),
                'last_page' => $tracks->lastPage(),
                'per_page' => $tracks->perPage(),
                'total' => $tracks->total(),
                'from' => $tracks->firstItem(),
                'to' => $tracks->lastItem(),
            ],
            'links' => [
                'first' => $tracks->url(1),
                'last' => $tracks->url($tracks->lastPage()),
                'prev' => $tracks->previousPageUrl(),
                'next' => $tracks->nextPageUrl(),
            ],
        ];
    }

    private function cursorPaginate($query, Request $request): array
    {
        $tracks = $query->cursorPaginate($request->get('per_page', 15))->withQueryString();

        return [
            'data' => TrackData::collection($tracks->items()),
            'meta' => [
                'per_page' => $tracks->perPage(),
                'has_more' => $tracks->hasMorePages(),
            ],
            'links' => [
                'prev' => $tracks->previousPageUrl(),
                'next' => $tracks->nextPageUrl(),
            ],
        ];
    }
}
```

## Laravel Data Integration

### Advanced Data Transfer Objects

```php
<?php

namespace App\Data;

use App\Models\Album;
use Spatie\LaravelData\Data;
use Spatie\LaravelData\Lazy;
use Spatie\LaravelData\Attributes\Validation\Required;
use Spatie\LaravelData\Attributes\Validation\Max;

class AlbumData extends Data
{
    public function __construct(
        public int $id,
        #[Required, Max(160)]
        public string $title,
        public int $artist_id,
        public Lazy|ArtistData|null $artist,
        /** @var TrackData[] */
        public Lazy|array $tracks,
        public ?string $release_date,
        public ?string $cover_image_url,
    ) {}

    public static function fromModel(Album $album): self
    {
        return new self(
            id: $album->id,
            title: $album->title,
            artist_id: $album->artist_id,
            artist: Lazy::whenLoaded('artist', $album, fn() => ArtistData::from($album->artist)),
            tracks: Lazy::whenLoaded('tracks', $album, fn() => TrackData::collection($album->tracks)),
            release_date: $album->release_date?->format('Y-m-d'),
            cover_image_url: $album->cover_image_url,
        );
    }
}

class ArtistData extends Data
{
    public function __construct(
        public int $id,
        #[Required, Max(120)]
        public string $name,
        /** @var AlbumData[] */
        public Lazy|array $albums,
        public ?string $biography,
        public ?string $website_url,
    ) {}

    public static function fromModel(Artist $artist): self
    {
        return new self(
            id: $artist->id,
            name: $artist->name,
            albums: Lazy::whenLoaded('albums', $artist, fn() => AlbumData::collection($artist->albums)),
            biography: $artist->biography,
            website_url: $artist->website_url,
        );
    }
}
```

### Request Data Validation

```php
<?php

namespace App\Data;

use Spatie\LaravelData\Data;
use Spatie\LaravelData\Attributes\Validation\Required;
use Spatie\LaravelData\Attributes\Validation\Min;
use Spatie\LaravelData\Attributes\Validation\Max;
use Spatie\LaravelData\Attributes\Validation\Numeric;

class TrackCreateData extends Data
{
    public function __construct(
        #[Required, Max(200)]
        public string $name,

        #[Max(220)]
        public ?string $composer,

        #[Required, Numeric, Min(1)]
        public int $milliseconds,

        #[Numeric, Min(0)]
        public ?int $bytes,

        #[Required, Numeric, Min(0)]
        public float $unit_price,

        #[Required, Numeric, Min(1)]
        public int $album_id,

        #[Required, Numeric, Min(1)]
        public int $media_type_id,

        #[Numeric, Min(1)]
        public ?int $genre_id,
    ) {}
}

class TrackUpdateData extends Data
{
    public function __construct(
        #[Max(200)]
        public ?string $name,

        #[Max(220)]
        public ?string $composer,

        #[Numeric, Min(1)]
        public ?int $milliseconds,

        #[Numeric, Min(0)]
        public ?int $bytes,

        #[Numeric, Min(0)]
        public ?float $unit_price,

        #[Numeric, Min(1)]
        public ?int $album_id,

        #[Numeric, Min(1)]
        public ?int $media_type_id,

        #[Numeric, Min(1)]
        public ?int $genre_id,
    ) {}
}
```

## Security Considerations

### Authorization Middleware

```php
<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class QueryBuilderSecurityMiddleware
{
    /**
     * Handle an incoming request
     */
    public function handle(Request $request, Closure $next): mixed
    {
        // Validate allowed parameters
        $this->validateQueryParameters($request);

        // Apply user-specific filters
        $this->applyUserFilters($request);

        // Rate limiting for complex queries
        $this->applyRateLimiting($request);

        return $next($request);
    }

    private function validateQueryParameters(Request $request): void
    {
        $allowedParams = ['filter', 'sort', 'include', 'fields', 'page', 'per_page'];
        $requestParams = array_keys($request->query());

        $invalidParams = array_diff($requestParams, $allowedParams);

        if (!empty($invalidParams)) {
            abort(400, 'Invalid query parameters: ' . implode(', ', $invalidParams));
        }
    }

    private function applyUserFilters(Request $request): void
    {
        $user = Auth::user();

        if (!$user) {
            return;
        }

        // Apply organization-specific filtering
        if ($user->organization_id) {
            $request->merge([
                'filter' => array_merge($request->get('filter', []), [
                    'organization_id' => $user->organization_id
                ])
            ]);
        }

        // Restrict sensitive fields for non-admin users
        if (!$user->hasRole('admin')) {
            $fields = $request->get('fields', []);
            $restrictedFields = ['internal_notes', 'cost_price', 'profit_margin'];

            foreach ($restrictedFields as $field) {
                if (($key = array_search($field, $fields)) !== false) {
                    unset($fields[$key]);
                }
            }

            if (!empty($fields)) {
                $request->merge(['fields' => $fields]);
            }
        }
    }

    private function applyRateLimiting(Request $request): void
    {
        $complexity = $this->calculateQueryComplexity($request);

        if ($complexity > 10) {
            // Apply stricter rate limiting for complex queries
            $key = 'complex_query:' . $request->ip();
            $attempts = cache()->increment($key);

            if ($attempts === 1) {
                cache()->put($key, 1, 60); // 1 minute window
            }

            if ($attempts > 5) {
                abort(429, 'Too many complex queries. Please try again later.');
            }
        }
    }

    private function calculateQueryComplexity(Request $request): int
    {
        $complexity = 0;

        // Add complexity for filters
        $filters = $request->get('filter', []);
        $complexity += count($filters) * 2;

        // Add complexity for includes
        $includes = explode(',', $request->get('include', ''));
        $complexity += count(array_filter($includes)) * 3;

        // Add complexity for sorting
        $sorts = explode(',', $request->get('sort', ''));
        $complexity += count(array_filter($sorts));

        return $complexity;
    }
}
```

### Input Sanitization

```php
<?php

namespace App\Services;

class QueryBuilderSanitizer
{
    /**
     * Sanitize filter values
     */
    public static function sanitizeFilters(array $filters): array
    {
        $sanitized = [];

        foreach ($filters as $key => $value) {
            // Remove potentially dangerous characters
            $sanitizedKey = preg_replace('/[^a-zA-Z0-9_.]/', '', $key);

            if (is_array($value)) {
                $sanitizedValue = array_map([self::class, 'sanitizeValue'], $value);
            } else {
                $sanitizedValue = self::sanitizeValue($value);
            }

            $sanitized[$sanitizedKey] = $sanitizedValue;
        }

        return $sanitized;
    }

    /**
     * Sanitize individual values
     */
    private static function sanitizeValue($value): string
    {
        // Remove SQL injection attempts
        $value = str_replace(['--', ';', 'UNION', 'SELECT', 'DROP', 'DELETE'], '', $value);

        // Limit length
        $value = substr($value, 0, 255);

        // Escape special characters
        return htmlspecialchars($value, ENT_QUOTES, 'UTF-8');
    }
}
```

## Performance Optimization

### Query Optimization Service

```php
<?php

namespace App\Services;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;

class QueryOptimizationService
{
    /**
     * Optimize query with caching and indexing hints
     */
    public static function optimizeQuery(Builder $query, array $options = []): Builder
    {
        // Add index hints for common filters
        if (isset($options['filters'])) {
            $query = self::addIndexHints($query, $options['filters']);
        }

        // Optimize eager loading
        if (isset($options['includes'])) {
            $query = self::optimizeEagerLoading($query, $options['includes']);
        }

        // Add query caching for expensive operations
        if ($options['cache'] ?? false) {
            $cacheKey = self::generateCacheKey($query, $options);
            $cacheTtl = $options['cache_ttl'] ?? 300; // 5 minutes

            return $query->remember($cacheTtl, $cacheKey);
        }

        return $query;
    }

    /**
     * Add database index hints
     */
    private static function addIndexHints(Builder $query, array $filters): Builder
    {
        $indexHints = [
            'genre_id' => 'idx_tracks_genre_id',
            'album_id' => 'idx_tracks_album_id',
            'unit_price' => 'idx_tracks_unit_price',
        ];

        foreach ($filters as $filter => $value) {
            if (isset($indexHints[$filter])) {
                $query->from(DB::raw("tracks USE INDEX ({$indexHints[$filter]})"));
                break; // Only use one index hint
            }
        }

        return $query;
    }

    /**
     * Optimize eager loading to prevent N+1 queries
     */
    private static function optimizeEagerLoading(Builder $query, array $includes): Builder
    {
        $optimizedIncludes = [];

        foreach ($includes as $include) {
            // Add select optimization for relationships
            switch ($include) {
                case 'album':
                    $optimizedIncludes['album:id,title,artist_id'] = function ($q) {
                        $q->select('id', 'title', 'artist_id');
                    };
                    break;
                case 'album.artist':
                    $optimizedIncludes['album.artist:id,name'] = function ($q) {
                        $q->select('id', 'name');
                    };
                    break;
                case 'genre':
                    $optimizedIncludes['genre:id,name'] = function ($q) {
                        $q->select('id', 'name');
                    };
                    break;
                default:
                    $optimizedIncludes[] = $include;
            }
        }

        return $query->with($optimizedIncludes);
    }

    /**
     * Generate cache key for query
     */
    private static function generateCacheKey(Builder $query, array $options): string
    {
        $sql = $query->toSql();
        $bindings = $query->getBindings();

        return 'query_builder:' . md5($sql . serialize($bindings) . serialize($options));
    }
}
```

## Testing Strategies

### Query Builder Tests

```php
<?php

namespace Tests\Feature\Api;

use Tests\TestCase;
use App\Models\Track;
use App\Models\Album;
use App\Models\Artist;
use App\Models\Genre;
use Illuminate\Foundation\Testing\RefreshDatabase;

class TrackQueryBuilderTest extends TestCase
{
    use RefreshDatabase;

    public function test_can_filter_tracks_by_name(): void
    {
        $track1 = Track::factory()->create(['name' => 'Test Track']);
        $track2 = Track::factory()->create(['name' => 'Another Track']);

        $response = $this->getJson('/api/tracks?filter[name]=Test');

        $response->assertOk()
            ->assertJsonCount(1, 'data')
            ->assertJsonPath('data.0.name', 'Test Track');
    }

    public function test_can_sort_tracks_by_multiple_fields(): void
    {
        $album = Album::factory()->create();

        Track::factory()->create([
            'name' => 'B Track',
            'unit_price' => 1.99,
            'album_id' => $album->id
        ]);

        Track::factory()->create([
            'name' => 'A Track',
            'unit_price' => 0.99,
            'album_id' => $album->id
        ]);

        $response = $this->getJson('/api/tracks?sort=unit_price,name');

        $response->assertOk()
            ->assertJsonPath('data.0.name', 'A Track')
            ->assertJsonPath('data.1.name', 'B Track');
    }

    public function test_can_include_relationships(): void
    {
        $artist = Artist::factory()->create(['name' => 'Test Artist']);
        $album = Album::factory()->create(['artist_id' => $artist->id]);
        $track = Track::factory()->create(['album_id' => $album->id]);

        $response = $this->getJson('/api/tracks/' . $track->id . '?include=album.artist');

        $response->assertOk()
            ->assertJsonStructure([
                'data' => [
                    'id',
                    'name',
                    'album' => [
                        'id',
                        'title',
                        'artist' => [
                            'id',
                            'name'
                        ]
                    ]
                ]
            ]);
    }

    public function test_validates_invalid_filters(): void
    {
        $response = $this->getJson('/api/tracks?filter[invalid_field]=value');

        $response->assertStatus(400);
    }

    public function test_pagination_works_correctly(): void
    {
        Track::factory()->count(25)->create();

        $response = $this->getJson('/api/tracks?per_page=10');

        $response->assertOk()
            ->assertJsonCount(10, 'data')
            ->assertJsonStructure([
                'data',
                'meta' => [
                    'current_page',
                    'last_page',
                    'per_page',
                    'total'
                ],
                'links'
            ]);
    }
}
```

## Best Practices

### API Design Guidelines

1. **Consistent Naming**: Use consistent parameter names across all endpoints
2. **Validation**: Always validate and sanitize input parameters
3. **Performance**: Implement caching for expensive queries
4. **Security**: Apply proper authorization and rate limiting
5. **Documentation**: Document all available filters, sorts, and includes

### Example API Documentation

```yaml
# OpenAPI specification example
paths:
  /api/tracks:
    get:
      summary: List tracks with filtering and sorting
      parameters:
        - name: filter[name]
          in: query
          description: Filter tracks by name (partial match)
          schema:
            type: string
        - name: filter[genre_id]
          in: query
          description: Filter tracks by genre ID (exact match)
          schema:
            type: integer
        - name: sort
          in: query
          description: Sort tracks by field(s)
          schema:
            type: string
            enum: [name, unit_price, milliseconds, -name, -unit_price, -milliseconds]
        - name: include
          in: query
          description: Include related resources
          schema:
            type: string
            enum: [album, album.artist, genre, mediaType]
      responses:
        200:
          description: Successful response
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    type: array
                    items:
                      $ref: '#/components/schemas/Track'
                  meta:
                    $ref: '#/components/schemas/PaginationMeta'
                  links:
                    $ref: '#/components/schemas/PaginationLinks'
```

This comprehensive guide provides everything needed to implement robust, secure, and performant APIs using Spatie Laravel Query Builder with Laravel Data integration. The combination offers type safety, flexibility, and excellent developer experience for building modern Laravel applications.

---

**Next Steps:**

- Explore [Spatie Comments System Guide](110-spatie-comments-guide.md) for user engagement features
- Review [Laravel WorkOS Guide](090-laravel-workos-guide.md) for enterprise authentication
- Check [Laravel Folio Guide](120-laravel-folio-guide.md) for modern routing patterns
